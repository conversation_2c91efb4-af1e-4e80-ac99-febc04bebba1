from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework_simplejwt.authentication import JWTAuthentication
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import MaintenanceWork
from .serializers import MaintenanceWorkSerializer
from rest_framework.response import Response
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time


@extend_schema(
    tags=["Maintenance"]
)
class MaintenanceWorkViewSet(viewsets.ModelViewSet):
    """
    ViewSet for MaintenanceWork model providing CRUD operations.
    """
    queryset = MaintenanceWork.objects.all()
    serializer_class = MaintenanceWorkSerializer
    pagination_class = CustomPagination
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [Django<PERSON>ilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['code','systemComponent','maintenanceType','scheduleDate','actualDate','technician','status','costEstimate']
    search_fields = ['code','systemComponent','maintenanceType','scheduleDate','actualDate','technician','status','costEstimate']
    
    def list(self, request, *args, **kwargs):
        code = request.query_params.get('code')
        systemComponent = request.query_params.get('systemComponent')
        maintenanceType = request.query_params.get('maintenanceType')
        scheduleDate = request.query_params.get('scheduleDate')
        actualDate = request.query_params.get('actualDate')
        technician = request.query_params.get('technician')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        
        if code:
            self.queryset = self.queryset.filter(code__icontains=code)
        if systemComponent:
            self.queryset = self.queryset.filter(systemComponent__icontains=systemComponent)
        if maintenanceType:
            self.queryset = self.queryset.filter(maintenanceType=maintenanceType)
        if scheduleDate:
            self.queryset = self.queryset.filter(scheduleDate__gte=convert_str_to_date_min_time(scheduleDate))
        if actualDate:
            self.queryset = self.queryset.filter(actualDate__lte=convert_str_to_date_max_time(actualDate))
        if technician:
            self.queryset = self.queryset.filter(technician__icontains=technician)
        if status:
            self.queryset = self.queryset.filter(status=status)
        if userId:
            self.queryset = self.queryset.filter(userId=userId)
        
        
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
