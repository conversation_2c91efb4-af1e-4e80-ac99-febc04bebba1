from django.db import models
from RTRDA.model import BaseModel


class Manpowerqualifications(BaseModel):
    code = models.Char<PERSON><PERSON>(db_column='Code', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.Char<PERSON>ield(db_column='Name', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    organization = models.CharField(db_column='Organization', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    qualification = models.Char<PERSON>ield(db_column='Qualification', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    fieldofstudy = models.CharField(db_column='FieldOfStudy', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    experienceyears = models.IntegerField(db_column='ExperienceYears')  # Field name made lowercase.
    skill = models.Char<PERSON><PERSON>(db_column='Skill', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    iscertification = models.BooleanField(db_column='IsCertification')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ManpowerQualifications'

